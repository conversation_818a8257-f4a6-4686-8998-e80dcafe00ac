<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Host Feedback Request - Togeda.ai</title>
    <style>
      /* Email-safe CSS with full responsive support */
      table {
        border-collapse: collapse;
        mso-table-lspace: 0pt;
        mso-table-rspace: 0pt;
      }

      img {
        border: 0;
        height: auto;
        line-height: 100%;
        outline: none;
        text-decoration: none;
        max-width: 100%;
      }

      body {
        margin: 0;
        padding: 0;
        width: 100% !important;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        background-color: #f5f5f5;
      }

      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #ffffff;
        font-family: Arial, sans-serif;
        width: 100% !important;
        min-width: 320px;
      }

      .email-header {
        background-color: #00796b;
        padding-top: 10px;
        text-align: center;
      }

      .email-header h1 {
        color: #ffffff;
        margin: 0;
        font-size: 24px;
        font-weight: 600;
      }

      .email-body {
        padding: 30px 20px;
        background-color: #ffffff;
      }

      .email-content {
        line-height: 1.6;
        color: #333333;
      }

      .email-content h2 {
        color: #00796b;
        margin-bottom: 15px;
        font-size: 20px;
      }

      .email-content p {
        margin-bottom: 15px;
        font-size: 16px;
      }

      .email-content ul {
        padding-left: 20px;
        margin-bottom: 15px;
      }

      .email-content li {
        margin-bottom: 8px;
        font-size: 16px;
      }

      .cta-button {
        display: inline-block;
        background-color: #ffd54f; /* Fallback for email clients that don't support gradients */
        background-image: linear-gradient(to bottom, #ffdb6a, #ffc107);
        color: #333333 !important; /* Dark text for readability */
        padding: 12px 30px;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        margin: 20px 0;
        font-size: 16px;
        min-width: 120px;
        text-align: center;
        box-sizing: border-box;
        border: 1px solid #ffc107; /* Optional: adds a border */
      }

      .cta-button:hover {
        background-color: #ffc107;
        background-image: linear-gradient(to bottom, #ffc107, #ffa000);
      }

      .details-box {
        background-color: #f8f9fa;
        border-left: 4px solid #00796b;
        padding: 15px;
        margin: 20px 0;
        border-radius: 0 8px 8px 0;
      }

      .details-box h3 {
        color: #00796b;
        margin: 0 0 10px 0;
        font-size: 18px;
      }

      .details-box p {
        margin: 5px 0;
        font-size: 14px;
      }

      .email-footer {
        background-color: #00796b;
        color: #ffffff;
        padding: 20px;
        text-align: center;
        font-size: 14px;
      }

      .email-footer a {
        color: #b2dfdb;
        text-decoration: none;
      }

      .status-badge {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 10px;
      }

      .host-feedback-badge {
        background-color: #ff5722; /* Deep Orange */
        color: white;
      }

      /* Mobile-first responsive design */
      @media only screen and (max-width: 640px) {
        .email-container {
          width: 100% !important;
          max-width: 100% !important;
        }

        .email-header {
          padding: 15px 10px !important;
        }

        .email-header h1 {
          font-size: 20px !important;
        }

        .email-body {
          padding: 20px 15px !important;
        }

        .email-content h2 {
          font-size: 18px !important;
          line-height: 1.3 !important;
        }

        .email-content p {
          font-size: 14px !important;
          line-height: 1.5 !important;
        }

        .cta-button {
          display: block !important;
          width: 100% !important;
          max-width: 280px !important;
          margin: 20px auto !important;
          padding: 15px 20px !important;
          font-size: 16px !important;
          text-align: center !important;
          box-sizing: border-box !important;
        }

        .details-box {
          padding: 12px !important;
          margin: 15px 0 !important;
        }

        .details-box h3 {
          font-size: 16px !important;
        }

        .details-box p {
          font-size: 13px !important;
        }

        .email-footer {
          padding: 15px 10px !important;
          font-size: 13px !important;
        }

        .status-badge {
          font-size: 11px !important;
          padding: 6px 12px !important;
        }
      }

      /* Large screens optimization */
      @media only screen and (min-width: 641px) {
        .email-container {
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          border-radius: 12px;
          overflow: hidden;
        }

        .email-header {
          border-radius: 12px 12px 0 0;
        }

        .email-footer {
          border-radius: 0 0 12px 12px;
        }
      }
    </style>
  </head>
  <body style="margin: 0; padding: 20px; background-color: #f5f5f5">
    <div class="email-container">
      <div class="email-header">
        <a href="https://www.togeda.ai" target="_blank">
          <img
            src="https://www.togeda.ai/togeda_logo_white.png"
            width="180"
            alt="Togeda.ai Logo"
            style="border: 0; max-width: 180px; height: auto; display: block; margin: 0 auto"
          />
        </a>
      </div>
      <div class="email-body">
        <div class="email-content">
          <div class="status-badge host-feedback-badge">Host Feedback</div>
          
          <h2>🏠 How Did Your Hosting Go?</h2>

          <p>Hi <strong>{{ params.recipientName }}</strong>,</p>

          <p>
            Thank you for hosting <strong>{{ params.guestName }}</strong> for your experience 
            <strong>{{ params.experienceTitle }}</strong>! We hope everything went smoothly 
            and that you had a great time sharing your passion with your guests.
          </p>

          <div class="details-box">
            <h3>📋 Experience Summary</h3>
            <p><strong>Experience:</strong> {{ params.experienceTitle }}</p>
            <p><strong>Guest:</strong> {{ params.guestName }}</p>
            <p><strong>Date:</strong> {{ params.bookingDate }}</p>
            <p><strong>Time:</strong> {{ params.bookingTime }}</p>
            <p><strong>Location:</strong> {{ params.experienceLocation }}</p>
            <p><strong>Booking ID:</strong> {{ params.bookingId }}</p>
          </div>

          <p>
            <strong>Your feedback helps us improve!</strong> As a valued host on our platform, 
            your insights about the guest experience are incredibly important. This feedback 
            helps us maintain quality standards and improve our service for everyone.
          </p>

          <div class="details-box">
            <h3>💭 We'd Love Your Thoughts On</h3>
            <ul>
              <li>What went well during the experience?</li>
              <li>Were there any challenges or areas for improvement?</li>
              <li>How would you rate this guest experience? (1-5 stars)</li>
            </ul>
          </div>

          <p>
            <strong>Quick & Easy:</strong> The feedback form takes just 2-3 minutes to complete 
            and helps us ensure great experiences for all hosts and guests on Togeda.ai.
          </p>

          <div style="text-align: center">
            <a href="{{ params.feedbackUrl }}" class="cta-button">Share Your Feedback</a>
          </div>

          <p>
            Thank you for being an amazing host and for contributing to the Togeda.ai community. 
            We look forward to supporting many more successful experiences with you!
          </p>

          <p>Best regards,<br /><strong>The Togeda.ai Host Team</strong></p>
        </div>
      </div>
      <div class="email-footer">
        <p>&copy; 2025 Togeda.ai. All rights reserved.</p>
        <p>Host Support: <a href="mailto:<EMAIL>"><EMAIL></a></p>
      </div>
    </div>
  </body>
</html>
